import 'package:flutter_test/flutter_test.dart';
import 'package:gather_point/feature/cancellation_policies/data/models/cancellation_policy_model.dart';
import 'package:gather_point/feature/home/<USER>/models/place_detail_model.dart';

void main() {
  group('Place Details Cancellation Policy Tests', () {
    test('should handle cancellation policy from API response', () {
      // Sample API response data based on the provided JSON
      final Map<String, dynamic> apiResponse = {
        "id": 46,
        "title": "test",
        "content": "test results",
        "price": 54,
        "city_id": 1,
        "service_category_id": 3,
        "user_id": 227,
        "views": 43,
        "created_at": "2025-06-28T07:37:24.000000Z",
        "updated_at": "2025-07-26T00:47:28.000000Z",
        "rating": 4.3,
        "no_of_rates": 20,
        "no_guests": 6,
        "beds": 3,
        "baths": 2,
        "booking_rules": "test booking rule",
        "cancelation_policy_id": 5,
        "cancelation_rules": null,
        "include_commission_daily": 0,
        "include_commission_weekly": 0,
        "include_commission_monthly": 0,
        "tourism_permit_number": "43254",
        "facilities": [],
        "favorite": false,
        "gallery": [],
        "country": "المملكة العربية السعودية",
        "city": "جدة",
        "url": "https://backend.gatherpoint.sa/service-category-item/46",
        "hoster": {
          "rating": 3.5,
          "name": "Test Host",
          "bio": "",
          "registered_since": "2025-06-23T23:15:16.000000Z",
          "total_no_of_rates": 14
        },
        "ratings": [],
        "cancellation_policy": {
          "id": 5,
          "name": "Strict - Long Term",
          "description": "Full refund if cancelled within 3 days of booking and at least 14 days before arrival.",
          "policy_type": "strict_long",
          "duration_type": "long",
          "cancellation_window_hours": 0,
          "refund_percentage": "100.00",
          "booking_window_hours": 72,
          "minimum_notice_hours": 336,
          "service_fee_refundable": false,
          "cleaning_fee_refundable": false,
          "is_active": true,
          "order": 5
        }
      };

      // Create PlaceDetailModel from API response
      final placeDetail = PlaceDetailModel.fromJson(apiResponse);

      // Verify that cancellation policy is properly parsed
      expect(placeDetail.cancellationPolicy, isNotNull);
      expect(placeDetail.cancellationPolicy!.id, equals(5));
      expect(placeDetail.cancellationPolicy!.name, equals("Strict - Long Term"));
      expect(placeDetail.cancellationPolicy!.description, equals("Full refund if cancelled within 3 days of booking and at least 14 days before arrival."));
      expect(placeDetail.cancellationPolicy!.policyType, equals("strict_long"));
      expect(placeDetail.cancellationPolicy!.durationType, equals("long"));
      expect(placeDetail.cancellationPolicy!.refundPercentage, equals(100.0));
      expect(placeDetail.cancellationPolicy!.bookingWindowHours, equals(72));
      expect(placeDetail.cancellationPolicy!.minimumNoticeHours, equals(336));
      expect(placeDetail.cancellationPolicy!.serviceFeeRefundable, equals(false));
      expect(placeDetail.cancellationPolicy!.cleaningFeeRefundable, equals(false));
    });

    test('should handle legacy cancellation rules when no policy exists', () {
      final Map<String, dynamic> apiResponse = {
        "id": 47,
        "title": "test legacy",
        "content": "test results",
        "price": 54,
        "city_id": 1,
        "service_category_id": 3,
        "user_id": 227,
        "views": 43,
        "created_at": "2025-06-28T07:37:24.000000Z",
        "updated_at": "2025-07-26T00:47:28.000000Z",
        "rating": 4.3,
        "no_of_rates": 20,
        "no_guests": 6,
        "beds": 3,
        "baths": 2,
        "booking_rules": "test booking rule",
        "cancelation_rules": "Free cancellation up to 24 hours before check-in",
        "include_commission_daily": 0,
        "include_commission_weekly": 0,
        "include_commission_monthly": 0,
        "tourism_permit_number": "43254",
        "facilities": [],
        "favorite": false,
        "gallery": [],
        "country": "المملكة العربية السعودية",
        "city": "جدة",
        "url": "https://backend.gatherpoint.sa/service-category-item/47",
        "hoster": {
          "rating": 3.5,
          "name": "Test Host",
          "bio": "",
          "registered_since": "2025-06-23T23:15:16.000000Z",
          "total_no_of_rates": 14
        },
        "ratings": [],
        "cancellation_policy": null
      };

      final placeDetail = PlaceDetailModel.fromJson(apiResponse);

      // Verify that legacy cancellation rules are preserved
      expect(placeDetail.cancellationPolicy, isNull);
      expect(placeDetail.cancelationRules, equals("Free cancellation up to 24 hours before check-in"));
    });

    test('should format cancellation policy display names correctly', () {
      final policy = CancellationPolicyModel(
        id: 1,
        nameEn: "Flexible Short Term",
        nameAr: "مرنة قصيرة المدى",
        name: "Flexible Short Term",
        descriptionEn: "Full refund 1 day before arrival",
        descriptionAr: "استرداد كامل قبل يوم واحد من الوصول",
        description: "Full refund 1 day before arrival",
        policyType: "flexible_short",
        durationType: "short",
        cancellationWindowHours: 24,
        refundPercentage: 100.0,
        serviceFeeRefundable: true,
        cleaningFeeRefundable: true,
        isActive: true,
        order: 1,
        createdAt: "2025-01-01T00:00:00.000000Z",
        updatedAt: "2025-01-01T00:00:00.000000Z",
      );

      expect(policy.policyTypeDisplayName, equals("مرنة - قصيرة المدى"));
      expect(policy.formattedDescription, equals("استرداد كامل قبل يوم واحد من الوصول"));
      expect(policy.formattedCancellationWindow, equals("1 day"));
    });
  });
}
