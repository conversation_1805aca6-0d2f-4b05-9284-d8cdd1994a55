<?php
namespace App\Http\Resources;

use App\Models\Admin\Facilities;
use App\Models\Admin\ServiceCategoryItemFavorite;
use App\Http\Resources\CancellationPolicyResource;
use Illuminate\Http\Resources\Json\JsonResource;

class ServiceCategoryItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $lang       = $request->hasHeader('X-localization') && in_array($request->Header('X-localization'), ['ar', 'en']) ? $request->Header('X-localization') : 'ar';
        $user       = auth('api')->user();
        $isFavorite = false;

        if ($user) {
            $fav = ServiceCategoryItemFavorite::where([
                'user_id'                  => $user->id,
                'service_category_item_id' => $this->id,
            ])->first();

            $isFavorite = ! is_null($fav);
        }

        // Group facilities by category, add 'Others' for those without category, and sort by order
        $facilitiesByCategory = [];
        $others = [];
        foreach ($this->facilities as $itemFacility) {
            $facility = $itemFacility->facility;
            if ($facility && $facility->category) {
                $catId = $facility->category->id;
                $catTitle = $facility->category->{'title_' . $lang} ?? $facility->category->title_en;
                $catOrder = $facility->category->order ?? 0;
                if (!isset($facilitiesByCategory[$catId])) {
                    $facilitiesByCategory[$catId] = [
                        'id' => $catId,
                        'title' => $catTitle,
                        'order' => $catOrder,
                        'facilities' => [],
                    ];
                }
                $facilitiesByCategory[$catId]['facilities'][] = [
                    'order' => $facility->order ?? 0,
                    'data' => new FacilitiesResource($facility)
                ];
            } else if ($facility) {
                $others[] = [
                    'order' => $facility->order ?? 0,
                    'data' => new FacilitiesResource($facility)
                ];
            }
        }
        // Sort facilities within each category by order
        foreach ($facilitiesByCategory as &$cat) {
            usort($cat['facilities'], function($a, $b) {
                return $a['order'] <=> $b['order'];
            });
            $cat['facilities'] = array_column($cat['facilities'], 'data');
        }
        unset($cat);
        // Sort categories by order
        usort($facilitiesByCategory, function($a, $b) {
            return $a['order'] <=> $b['order'];
        });
        // Sort others by order
        usort($others, function($a, $b) {
            return $a['order'] <=> $b['order'];
        });
        $others = array_column($others, 'data');
        $categories = $facilitiesByCategory;
        if (count($others)) {
            $categories[] = [
                'id' => null,
                'title' => __('Others'),
                'order' => 9999,
                'facilities' => $others,
            ];
        }
        return array_merge(parent::toArray($request), [
            'favorite'              => $isFavorite,
            'image'                 => ($this->gallery && $this->gallery->isNotEmpty() && $this->gallery->first()->image)
            ? asset('storage/' . $this->gallery->first()->image)
            : asset('assets/default-image.jpg'),
            'gallery'               => $this->gallery ? ServiceCategoryItemGalleryResource::collection($this->gallery) : [],
            'facilities'               => $this->facilities ? ServiceCategoryItemFacilitiesResource::collection($this->facilities) : [],
            'facilities_by_category' => $categories,
            'country'               => $this->city->country->{'title_' . $lang} ?? null,
            'city'                  => $this->city->{'title_' . $lang} ?? null,
            'lat'                   => $this->lat ?? 21.5292,
            'lon'                   => $this->lon ?? 39.1611,
            'price'                 => $this->price,
            'rating'                => $this->rating ?? 4.3,     // Assuming a rating attribute exists
            'no_of_rates'           => $this->no_of_rates ?? 20, // Assuming a no_of_rates attribute exists
            'title'                 => $this->title,
            'content'               => $this->content,
            'no_guests'             => $this->no_guests ?? 5, // Assuming a no_guests attribute exists
            'beds'                  => $this->beds ?? 3,      // Assuming a beds attribute exists
            'baths'                 => $this->baths ?? 2,     // Assuming a baths attribute exists
            'tourism_permit_number' => $this->tourism_permit_number,
            'url'                   => url('service-category-item' . '/' . $this->id),
            'hoster'                => [
                'rating'            => $this->user->rating ?? 3.5, // Assuming user relationship has a rating attribute
                'name'              => $this->user->name ?? null,
                'bio'              => $this->user->bio ?? "Test Bio",
                'registered_since'  => $this->user->created_at ?? null,
                'total_no_of_rates' => $this->user->total_no_of_rates ?? 14, // Assuming user has total_no_of_rates
            ],
            'ratings'               => [
                [
                    'id'         => 1,
                    'user_id'    => 1,
                    'user_name' => "Faisal Hakami",
                    'value'     => 5,
                    'comment'    => 'This is a great place!',
                    'created_at' => '2024-01-10',
                ],
                [
                    'id'         => 2,
                    'user_id'    => 2,
                    'user_name' => "Mohamed Shady",
                    'value'     => 4,
                    'comment'    => 'Good place to stay.',
                    'created_at' => '2024-01-08',
                ],
            ],
            'booking_rules'         => $this->booking_rules,     // Assuming a booking_rules attribute exists
            'cancelation_rules'     => $this->cancelation_rules, // Backward compatibility
            'cancellation_policy'   => $this->cancellationPolicy ? new CancellationPolicyResource($this->cancellationPolicy) : null,
        ]);
    }
}
