<?php
namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CancellationPolicyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $lang = $request->hasHeader('X-localization') && in_array($request->Header('X-localization'), ['ar', 'en']) ? $request->Header('X-localization') : 'ar';
        return [
            'id'                        => $this->id,
            'name'                      => $this->{'name_' . $lang},
            'description'            => $this->{'description_' . $lang},
            'policy_type'               => $this->policy_type,
            'duration_type'             => $this->duration_type,
            'cancellation_window_hours' => $this->cancellation_window_hours,
            'refund_percentage'         => $this->refund_percentage,
            'booking_window_hours'      => $this->booking_window_hours,
            'minimum_notice_hours'      => $this->minimum_notice_hours,
            'service_fee_refundable'    => $this->service_fee_refundable,
            'cleaning_fee_refundable'   => $this->cleaning_fee_refundable,
            'is_active'                 => $this->is_active,
            'order'                     => $this->order,
        ];
    }
}
