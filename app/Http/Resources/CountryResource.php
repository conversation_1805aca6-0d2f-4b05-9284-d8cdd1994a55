<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CountryResource extends JsonResource
{
    /**
     * User resource construct.
     *
     * @param  mixed  $collection
     * @param  \Laravel\Passport\Token|null  $token
     * @param  String|null  $accessToken
     */
    public function __construct($collection)
    {
        parent::__construct($collection);
    }

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $lang = $request->hasHeader('X-localization') && in_array($request->Header('X-localization'), ['ar', 'en']) ? $request->Header('X-localization') : 'ar';
        $resource = [
                'id' => (int) $this->id,
                'name' => $this->{'title_' . $lang},
                'created_at' => $this->created_at,
        ];
        return $resource;
    }

}
