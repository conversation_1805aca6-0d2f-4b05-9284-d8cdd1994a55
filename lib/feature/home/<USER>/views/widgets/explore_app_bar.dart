import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/utils/guest_reservation_handler.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/feature/home/<USER>/views/widgets/explore_list_widgets.dart';
import 'package:gather_point/generated/l10n.dart';

class ExploreAppBar extends StatelessWidget {
  final String categoryTitle;
  final List<City> cities;
  final City? selectedCity;
  final bool isLoadingLocation;
  final Function(City) onCitySelected;
  final TextEditingController searchController;

  const ExploreAppBar({
    super.key,
    required this.categoryTitle,
    required this.cities,
    required this.selectedCity,
    required this.isLoadingLocation,
    required this.onCitySelected,
    required this.searchController,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SliverAppBar(
      expandedHeight: 180,
      floating: false,
      pinned: true,
      backgroundColor: theme.colorScheme.primary,
      title: Text(
        categoryTitle,
        style: AppTextStyles.font18Bold.copyWith(color: Colors.white),
      ),
      centerTitle: false,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.primary,
                theme.colorScheme.primary.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 80, 16, 10),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CitySelectionWidget(
                  selectedCity: selectedCity,
                  cities: cities,
                  isLoading: isLoadingLocation,
                  onCitySelected: onCitySelected,
                ),
                const SizedBox(height: 12),
                Flexible(
                  child: _buildSearchField(context, s, theme),
                ),
              ],
            ),
          ),
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        GuestReservationHandler.getUserStatusBadge(context),
        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildSearchField(BuildContext context, S s, ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: searchController,
        style: const TextStyle(color: Colors.black),
        decoration: InputDecoration(
          prefixIcon: Icon(Icons.search, color: theme.colorScheme.primary),
          suffixIcon: searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear, color: Colors.grey),
                  onPressed: () {
                    searchController.clear();
                    FocusScope.of(context).unfocus();
                  },
                )
              : null,
          hintText: s.searchHint,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        ),
      ),
    );
  }
}
