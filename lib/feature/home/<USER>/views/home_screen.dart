import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/managers/locale_cubit/locale_cubit.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/services/location_service.dart';

import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/feature/home/<USER>/cubit/home_cubit.dart';
import 'package:gather_point/feature/home/<USER>/widgets/home_reels_section.dart';
import 'package:gather_point/feature/home/<USER>/widgets/home_categories_section.dart';

import 'package:gather_point/feature/home/<USER>/widgets/home_search_results.dart';
import 'package:gather_point/feature/settings/presentation/views/settings_view.dart';
import 'package:hive/hive.dart';

class GatherPointHome extends StatelessWidget {
  const GatherPointHome({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => HomeCubit(
        dioConsumer: DioConsumer(
          dio: getIt<Dio>(),
          profileBox: getIt<Box<UserEntity>>(),
        ),
        locationService: getIt<LocationService>(),
      )..initializeHome(),
      child: const _GatherPointHomeContent(),
    );
  }
}

class _GatherPointHomeContent extends StatefulWidget {
  const _GatherPointHomeContent();

  @override
  State<_GatherPointHomeContent> createState() =>
      _GatherPointHomeContentState();
}

class _GatherPointHomeContentState extends State<_GatherPointHomeContent> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _showCityDialog(List<City> cities, City? currentCity) {
    final s = S.of(context);
    // Capture the HomeCubit reference before showing dialog
    final homeCubit = context.read<HomeCubit>();

    showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          backgroundColor: dialogContext.cardColor,
          title: Text(s.selectCity,
              style: AppTextStyles.font18Bold
                  .copyWith(color: dialogContext.primaryTextColor)),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: cities.length,
              itemBuilder: (context, index) {
                final city = cities[index];
                return ListTile(
                  title: Text(city.name,
                      style: AppTextStyles.font16Regular
                          .copyWith(color: dialogContext.primaryTextColor)),
                  hoverColor: dialogContext.accentColor.withValues(alpha: 0.1),
                  onTap: () {
                    debugPrint(
                        'User selected city: ${city.name} (ID: ${city.id})');
                    // Use the captured cubit reference instead of context.read
                    homeCubit.selectCity(city);
                    Navigator.pop(dialogContext);
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  void _performSearch(String query) {
    try {
      context.read<HomeCubit>().performSearch(query);
    } catch (e) {
      debugPrint('HomeCubit not available for search: $e');
    }
  }

  void _clearSearch() {
    _searchController.clear();
    FocusScope.of(context).unfocus();
    try {
      context.read<HomeCubit>().clearSearch();
    } catch (e) {
      debugPrint('HomeCubit not available for clear search: $e');
    }
  }

  /// Enhanced refresh functionality with animations and haptic feedback
  Future<void> _onRefresh() async {
    try {
      // Add haptic feedback
      HapticFeedback.mediumImpact();

      // Trigger refresh in cubit
      await context.read<HomeCubit>().initializeHome();

      // Add success haptic feedback
      HapticFeedback.lightImpact();
    } catch (e) {
      debugPrint('Refresh error: $e');
      // Add error haptic feedback
      HapticFeedback.heavyImpact();

      // Show error snackbar with animation
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في تحديث البيانات',
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.white,
              ),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: const EdgeInsets.all(16),
            duration: const Duration(seconds: 3),
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: _onRefresh,
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return BlocConsumer<HomeCubit, HomeState>(
      listener: (context, state) {
        if (state is HomeLoaded && state.searchResults.isNotEmpty) {
          HomeSearchResults.show(
              context, state.searchResults, _searchController.text);
        }
      },
      builder: (context, state) {
        return BlocListener<LocaleCubit, LocaleState>(
          listener: (context, localeState) {
            // Only call updateLocalization if HomeCubit is available
            try {
              context.read<HomeCubit>().updateLocalization();
            } catch (e) {
              // HomeCubit not available yet, ignore
              debugPrint('HomeCubit not available for locale update: $e');
            }
          },
          child: _buildHomeContent(context, state, s),
        );
      },
    );
  }

  Widget _buildHomeContent(BuildContext context, HomeState state, S s) {
    if (state is HomeLoading) {
      return _buildOptimizedLoadingScreen(context);
    }

    if (state is HomeError) {
      return _buildErrorScreen(context, state.message, s);
    }

    final homeState = state as HomeLoaded;

    return Scaffold(
      backgroundColor: context.backgroundColor,
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: _onRefresh,
          color: context.accentColor,
          backgroundColor: context.backgroundColor,
          strokeWidth: 2,
          displacement: 40,
          child: CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            slivers: [
              // Header Section
              SliverToBoxAdapter(
                child: _buildOptimizedHeader(context, homeState, s),
              ),

              // Search Bar Section
              SliverToBoxAdapter(
                child: _buildOptimizedSearchBar(context, homeState, s),
              ),

              // Categories Section
              SliverToBoxAdapter(
                child: HomeCategoriesSection(
                  categories: homeState.categories,
                  isLoading: homeState.isLoadingCategories,
                ),
              ),

              // Reels Section
              SliverToBoxAdapter(
                child: HomeReelsSection(
                  reels: homeState.reels,
                  isLoading: homeState.isLoadingReels,
                  pagination: homeState.reelsPagination,
                ),
              ),

              // Bottom padding
              const SliverToBoxAdapter(
                child: SizedBox(height: 50),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOptimizedHeader(BuildContext context, HomeLoaded homeState, S s) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Simple logo without animations
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: context.accentColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.location_on,
              color: Colors.white,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          // App Name and Location
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  s.appName,
                  style: AppTextStyles.font20Bold.copyWith(
                    color: context.primaryTextColor,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                // Simple location selector
                GestureDetector(
                  onTap: homeState.cities.isNotEmpty
                      ? () => _showCityDialog(homeState.cities, homeState.currentCity)
                      : null,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: context.accentColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 14,
                          color: context.accentColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          homeState.currentCity?.name ?? s.detectingLocation,
                          style: AppTextStyles.font12SemiBold.copyWith(
                            color: context.accentColor,
                          ),
                        ),
                        if (homeState.cities.isNotEmpty) ...[
                          const SizedBox(width: 4),
                          Icon(
                            Icons.keyboard_arrow_down,
                            size: 14,
                            color: context.accentColor,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Settings button
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SettingsView(),
                ),
              );
            },
            icon: Icon(
              Icons.settings,
              color: context.secondaryTextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptimizedSearchBar(BuildContext context, HomeLoaded homeState, S s) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: TextField(
        controller: _searchController,
        onChanged: (value) => setState(() {}),
        onTap: () => _performSearch(_searchController.text),
        onSubmitted: _performSearch,
        style: AppTextStyles.font16Regular.copyWith(
          color: context.primaryTextColor,
        ),
        decoration: InputDecoration(
          hintText: s.searchHint,
          hintStyle: AppTextStyles.font16Regular.copyWith(
            color: context.secondaryTextColor,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: context.accentColor,
          ),
          suffixIcon: homeState.isSearching
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : _searchController.text.isNotEmpty
                  ? IconButton(
                      onPressed: _clearSearch,
                      icon: const Icon(Icons.clear),
                    )
                  : null,
          filled: true,
          fillColor: context.cardColor,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),
    );
  }

  Widget _buildOptimizedLoadingScreen(BuildContext context) {
    return Scaffold(
      backgroundColor: context.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Simple header shimmer
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 120,
                          height: 20,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          width: 80,
                          height: 16,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Simple search bar shimmer
            Container(
              margin: const EdgeInsets.all(16),
              height: 48,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            // Simple categories shimmer
            Container(
              height: 100,
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: List.generate(
                  4,
                  (index) => Container(
                    width: 80,
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            // Simple reels shimmer
            Expanded(
              child: ListView.builder(
                itemCount: 3,
                itemBuilder: (context, index) => Container(
                  height: 200,
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildErrorScreen(BuildContext context, String message, S s) {
    return Scaffold(
      backgroundColor: context.backgroundColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: context.secondaryTextColor,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: AppTextStyles.font16Regular.copyWith(
                color: context.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                try {
                  context.read<HomeCubit>().initializeHome();
                } catch (e) {
                  debugPrint('HomeCubit not available for retry: $e');
                  // Optionally navigate back or show a different error
                }
              },
              child: Text(s.retry),
            ),
          ],
        ),
      ),
    );
  }
}
