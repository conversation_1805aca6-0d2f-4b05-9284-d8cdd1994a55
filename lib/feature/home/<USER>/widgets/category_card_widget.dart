import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';

class CategoryCardWidget extends StatelessWidget {
  final String title;
  final String? iconUrl;

  const CategoryCardWidget({
    super.key,
    required this.title,
    this.iconUrl,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 80,
      margin: const EdgeInsets.only(right: 12),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Simple container without animations
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: context.accentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: context.accentColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Center(
              child: iconUrl != null && iconUrl!.isNotEmpty
                  ? Image.network(
                      iconUrl!,
                      width: 32,
                      height: 32,
                      errorBuilder: (context, error, stackTrace) => Icon(
                        Icons.category,
                        color: context.accentColor,
                        size: 32,
                      ),
                    )
                  : Icon(
                      Icons.category,
                      color: context.accentColor,
                      size: 32,
                    ),
            ),
          ),
          const SizedBox(height: 8),
          // Title
          Text(
            title,
            style: AppTextStyles.font12SemiBold.copyWith(
              color: context.primaryTextColor,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}


