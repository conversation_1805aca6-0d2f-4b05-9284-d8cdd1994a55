import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';

class CategoryCardWidget extends StatefulWidget {
  final String title;
  final String? iconUrl;

  const CategoryCardWidget({
    super.key,
    required this.title,
    this.iconUrl,
  });

  @override
  State<CategoryCardWidget> createState() => _CategoryCardWidgetState();
}

class _CategoryCardWidgetState extends State<CategoryCardWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // Simple scale animation for entrance
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    // Start animation with a small delay based on title hash for staggered effect
    Future.delayed(Duration(milliseconds: 50 + (widget.title.hashCode % 200)), () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: 80,
              margin: const EdgeInsets.only(right: 12),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Beautiful container with subtle glow effect
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: context.accentColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: context.accentColor.withValues(alpha: 0.3),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: context.accentColor.withValues(alpha: 0.1),
                          blurRadius: 8,
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Center(
                      child: widget.iconUrl != null && widget.iconUrl!.isNotEmpty
                          ? Image.network(
                              widget.iconUrl!,
                              width: 32,
                              height: 32,
                              errorBuilder: (context, error, stackTrace) => Icon(
                                Icons.category,
                                color: context.accentColor,
                                size: 32,
                              ),
                            )
                          : Icon(
                              Icons.category,
                              color: context.accentColor,
                              size: 32,
                            ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  // Title
                  Text(
                    widget.title,
                    style: AppTextStyles.font12SemiBold.copyWith(
                      color: context.primaryTextColor,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}


