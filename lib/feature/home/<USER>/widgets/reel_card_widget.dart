import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';

import 'package:gather_point/feature/reels/presentation/views/single_reel_page.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/utils/sound_manager.dart';

class ReelCardWidget extends StatelessWidget {
  final Map<String, dynamic> item;
  final List<Map<String, dynamic>> reelsData;
  final int index;
  final VoidCallback? onTap; // Custom tap callback

  const ReelCardWidget({
    super.key,
    required this.item,
    required this.reelsData,
    required this.index,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final imageUrl = item['images']?[0]?['url'] ?? '';
    final title = item['title'] ?? s.noTitle;
    final price = item['price']?.toString() ?? '0';
    final currency = Localizations.localeOf(context).languageCode == 'ar' ? 'ر.س' : 'SR';

    return Container(
      width: 160,
      margin: const EdgeInsets.symmetric(horizontal: 8),
      child: GestureDetector(
        onTap: () => _handleTap(context),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: context.cardColor,
            boxShadow: [
              BoxShadow(
                color: context.accentColor.withValues(alpha: 0.1),
                blurRadius: 8,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image with play button overlay
              Expanded(
                child: Stack(
                  children: [
                    // Main image
                    ClipRRect(
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                      child: CachedNetworkImage(
                        imageUrl: imageUrl,
                        width: double.infinity,
                        height: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: Colors.grey[300],
                          child: const Center(
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: Colors.grey[300],
                          child: Icon(
                            Icons.image_not_supported,
                            color: Colors.grey[600],
                            size: 40,
                          ),
                        ),
                      ),
                    ),
                    // Play button overlay
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withValues(alpha: 0.3),
                            ],
                          ),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.play_circle_filled,
                            color: Colors.white,
                            size: 40,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Content section
              Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      title,
                      style: AppTextStyles.font14SemiBold.copyWith(
                        color: context.primaryTextColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    // Price
                    Text(
                      '$price $currency',
                      style: AppTextStyles.font12SemiBold.copyWith(
                        color: context.accentColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleTap(BuildContext context) async {
    // Play click sound
    try {
      await SoundManager.playClickSound();
    } catch (e) {
      debugPrint('Click sound error: $e');
    }

    if (context.mounted) {
      if (onTap != null) {
        onTap!();
      } else {
        // Default navigation to single reel page
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SingleReelPage(
              reelsData: reelsData,
              selectedIndex: index,
            ),
          ),
        );
      }
    }
  }
}
