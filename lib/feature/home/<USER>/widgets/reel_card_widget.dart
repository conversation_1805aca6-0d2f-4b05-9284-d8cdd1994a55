import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';

import 'package:gather_point/feature/reels/presentation/views/single_reel_page.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/utils/sound_manager.dart';

class ReelCardWidget extends StatelessWidget {
  final Map<String, dynamic> item;
  final List<Map<String, dynamic>> reelsData;
  final int index;
  final VoidCallback? onTap; // Custom tap callback

  const ReelCardWidget({
    super.key,
    required this.item,
    required this.reelsData,
    required this.index,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // Fix image URL extraction to match the original data structure
    final imageUrl = (item['gallery'] as List?)?.isNotEmpty == true
        ? item['gallery'][0]['image']
        : item['image'] ?? 'https://via.placeholder.com/280x200';

    final title = item['title'] ?? s.noTitle;
    final price = item['price']?.toString() ?? '0';

    return Container(
      width: 280, // Original width
      margin: const EdgeInsets.only(right: 16), // Original margin
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20), // Original border radius
        boxShadow: context.cardShadow, // Original shadow
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () => _handleTap(context),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Stack(
              children: [
                // Background Image - Fixed with proper caching
                CachedNetworkImage(
                  imageUrl: imageUrl,
                  width: 280,
                  height: 200, // Original height
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    width: 280,
                    height: 200,
                    color: context.secondaryTextColor.withValues(alpha: 0.1),
                    child: const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
                        strokeWidth: 2,
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    width: 280,
                    height: 200,
                    color: context.secondaryTextColor.withValues(alpha: 0.1),
                    child: Icon(
                      Icons.image_not_supported_rounded,
                      color: context.secondaryTextColor,
                      size: 48,
                    ),
                  ),
                  memCacheWidth: 560, // 2x for high DPI
                  memCacheHeight: 400,
                  maxWidthDiskCache: 840, // 3x for very high DPI
                  maxHeightDiskCache: 600,
                ),
                // Gradient Overlay
                Container(
                  width: 280,
                  height: 200,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.7),
                      ],
                    ),
                  ),
                ),
                // Content overlay - Original layout
                Positioned(
                  bottom: 16,
                  left: 16,
                  right: 16,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (title.isNotEmpty)
                        Text(
                          title,
                          style: AppTextStyles.font16Bold.copyWith(
                            color: Colors.white,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: isDark
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(context).colorScheme.secondary,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              '$price ${s.perNight}',
                              style: AppTextStyles.font12Bold.copyWith(
                                color: isDark
                                    ? Theme.of(context).colorScheme.secondary
                                    : Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ),
                          const Spacer(),
                          // Simple play button without animation for performance
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Icon(
                              Icons.play_arrow,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleTap(BuildContext context) async {
    // Play click sound
    try {
      await SoundManager.playClickSound();
    } catch (e) {
      debugPrint('Click sound error: $e');
    }

    if (context.mounted) {
      if (onTap != null) {
        onTap!();
      } else {
        // Default navigation to single reel page
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SingleReelPage(
              showBackButton: true,
              reelsData: reelsData,
              selectedIndex: index,
            ),
          ),
        );
      }
    }
  }
}
