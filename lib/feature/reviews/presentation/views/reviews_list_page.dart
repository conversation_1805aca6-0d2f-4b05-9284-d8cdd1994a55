import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/feature/reviews/presentation/cubit/review_cubit.dart';
import 'package:gather_point/feature/reviews/data/models/review_model.dart';
import 'package:gather_point/feature/reviews/data/services/reviews_api_service.dart';
import 'package:gather_point/core/services/service_locator.dart';

class ReviewsListPage extends StatelessWidget {
  final int propertyId;
  final String propertyName;

  const ReviewsListPage({
    super.key,
    required this.propertyId,
    required this.propertyName,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ReviewCubit(getIt<ReviewsApiService>())
        ..loadPropertyReviewsWithStats(propertyId: propertyId),
      child: _ReviewsListPageContent(
        propertyId: propertyId,
        propertyName: propertyName,
      ),
    );
  }
}

class _ReviewsListPageContent extends StatefulWidget {
  final int propertyId;
  final String propertyName;

  const _ReviewsListPageContent({
    required this.propertyId,
    required this.propertyName,
  });

  @override
  State<_ReviewsListPageContent> createState() => _ReviewsListPageContentState();
}

class _ReviewsListPageContentState extends State<_ReviewsListPageContent> {
  String _sortBy = 'newest';
  double? _minRating;

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedPageLayout(
      title: s.reviews,
      body: BlocBuilder<ReviewCubit, ReviewState>(
        builder: (context, state) {
          if (state is ReviewLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (state is ReviewError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red.withValues(alpha: 0.6),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    s.reviewsLoadError,
                    style: AppTextStyles.font16SemiBold,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    style: AppTextStyles.font14Regular.copyWith(
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  EnhancedButton(
                    text: s.retry,
                    onPressed: () {
                      context.read<ReviewCubit>().loadPropertyReviewsWithStats(
                        propertyId: widget.propertyId,
                      );
                    },
                    icon: Icons.refresh,
                  ),
                ],
              ),
            );
          }

          if (state is ReviewsWithStatsLoaded) {
            return Column(
              children: [
                // Stats Header
                _buildStatsHeader(state.stats),
                
                // Filters
                _buildFilters(),
                
                // Reviews List
                Expanded(
                  child: _buildReviewsList(state.reviews),
                ),
              ],
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildStatsHeader(ReviewStatsModel stats) {
    final s = S.of(context);
    return Container(
      padding: const EdgeInsets.all(20),
      child: EnhancedCard(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Column(
                    children: [
                      Text(
                        stats.averageRating.toStringAsFixed(1),
                        style: AppTextStyles.font30SemiBold.copyWith(
                          color: Colors.amber,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: List.generate(5, (index) {
                          return Icon(
                            index < stats.averageRating 
                                ? Icons.star 
                                : Icons.star_border,
                            color: Colors.amber,
                            size: 20,
                          );
                        }),
                      ),
                    ],
                  ),
                  Container(
                    height: 60,
                    width: 1,
                    color: Colors.grey[300],
                  ),
                  Column(
                    children: [
                      Text(
                        stats.totalReviews.toString(),
                        style: AppTextStyles.font30SemiBold.copyWith(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        s.reviews,
                        style: AppTextStyles.font14Regular.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              if (stats.ratingDistribution.isNotEmpty) ...[
                const SizedBox(height: 20),
                _buildRatingDistribution(stats.ratingDistribution),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRatingDistribution(Map<int, int> distribution) {
    final total = distribution.values.fold(0, (sum, count) => sum + count);
    
    return Column(
      children: [
        for (int rating = 5; rating >= 1; rating--)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              children: [
                Text(
                  '$rating',
                  style: AppTextStyles.font12Regular,
                ),
                const SizedBox(width: 8),
                const Icon(
                  Icons.star,
                  color: Colors.amber,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: LinearProgressIndicator(
                    value: total > 0 
                        ? (distribution[rating] ?? 0) / total 
                        : 0.0,
                    backgroundColor: Colors.grey[300],
                    valueColor: const AlwaysStoppedAnimation<Color>(Colors.amber),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${distribution[rating] ?? 0}',
                  style: AppTextStyles.font12Regular,
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildFilters() {
    final s = S.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Row(
        children: [
          // Sort Dropdown
          Expanded(
            child: DropdownButtonFormField<String>(
              value: _sortBy,
              decoration: InputDecoration(
                labelText: s.sortBy,
                border: const OutlineInputBorder(),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: [
                DropdownMenuItem(value: 'newest', child: Text(s.newest)),
                DropdownMenuItem(value: 'oldest', child: Text(s.oldest)),
                DropdownMenuItem(value: 'highest_rated', child: Text(s.highestRated)),
                DropdownMenuItem(value: 'lowest_rated', child: Text(s.lowestRated)),
              ],
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                });
                _applyFilters();
              },
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Rating Filter
          Expanded(
            child: DropdownButtonFormField<double?>(
              value: _minRating,
              decoration: InputDecoration(
                labelText: s.minRating,
                border: const OutlineInputBorder(),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: [
                DropdownMenuItem(value: null, child: Text(s.all)),
                DropdownMenuItem(value: 4.0, child: Text(s.fourPlusStars)),
                DropdownMenuItem(value: 3.0, child: Text(s.threePlusStars)),
                DropdownMenuItem(value: 2.0, child: Text(s.twoPlusStars)),
                DropdownMenuItem(value: 1.0, child: Text(s.onePlusStars)),
              ],
              onChanged: (value) {
                setState(() {
                  _minRating = value;
                });
                _applyFilters();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewsList(List<ReviewModel> reviews) {
    final s = S.of(context);
    if (reviews.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.star_outline_rounded,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              s.noReviews,
              style: AppTextStyles.font16SemiBold.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              s.beFirstToReview,
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: reviews.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: ReviewCard(review: reviews[index]),
        );
      },
    );
  }

  void _applyFilters() {
    context.read<ReviewCubit>().loadPropertyReviewsWithStats(
      propertyId: widget.propertyId,
      sortBy: _sortBy,
      minRating: _minRating,
    );
  }
}

class ReviewCard extends StatelessWidget {
  final ReviewModel review;

  const ReviewCard({
    super.key,
    required this.review,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User Info and Rating
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundImage: review.user?.avatar != null
                      ? NetworkImage(review.user!.avatar!)
                      : null,
                  child: review.user?.avatar == null
                      ? Text(
                          review.user?.name.substring(0, 1).toUpperCase() ?? 'U',
                          style: AppTextStyles.font14Bold.copyWith(
                            color: Colors.white,
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        review.user?.name ?? 'مستخدم',
                        style: AppTextStyles.font14Bold,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        _formatDate(review.createdAt),
                        style: AppTextStyles.font12Regular.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    const Icon(
                      Icons.star,
                      color: Colors.amber,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      review.rating.toString(),
                      style: AppTextStyles.font14Bold.copyWith(
                        color: Colors.amber[800],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Comment
            Text(
              review.comment,
              style: AppTextStyles.font14Regular.copyWith(
                height: 1.4,
              ),
            ),
            
            // Images
            if (review.images != null && review.images!.isNotEmpty) ...[
              const SizedBox(height: 12),
              SizedBox(
                height: 80,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: review.images!.length,
                  itemBuilder: (context, index) {
                    return Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          review.images![index].imageUrl,
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 80,
                              height: 80,
                              color: Colors.grey[300],
                              child: const Icon(Icons.image, color: Colors.grey),
                            );
                          },
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 30) {
      return '${date.day}/${date.month}/${date.year}';
    } else if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ قليل';
    }
  }
}
