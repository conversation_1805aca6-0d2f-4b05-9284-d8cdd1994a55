import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/managers/locale_cubit/locale_cubit.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/feature/reservations/presentation/views/reservation_confirmation_page.dart';
import 'package:gather_point/feature/reservations/presentation/widget/calendar_widget.dart';
import 'package:gather_point/feature/reels/domain/entities/comment_entity.dart';
import 'package:gather_point/feature/reels/presentation/views/widgets/comment_dialog.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:intl/intl.dart';
import 'package:video_player/video_player.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:hive/hive.dart';
import 'package:go_router/go_router.dart';
import 'package:gather_point/core/routing/routes_keys.dart';
import 'package:gather_point/feature/home/<USER>/views/place_details_screen.dart';

import 'facility_icon_widget.dart';

class VideoPlayerWidget extends StatefulWidget {
  final List<dynamic> facilities;
  final bool favorite;
  final String videoUrl;
  final String title;
  final String location;
  final num price;
  final int serviceCategoryId;
  final int id;
  final DioConsumer dioConsumer;

  const VideoPlayerWidget({
    super.key,
    required this.facilities,
    required this.videoUrl,
    required this.title,
    required this.location,
    required this.price,
    required this.serviceCategoryId,
    required this.id,
    required this.dioConsumer,
    required this.favorite,
  });

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  VideoPlayerController? _controller;
  bool isLiked = false;
  bool _isLoading = true;
  bool _hasError = false;
  bool _isMuted = true;
  String? _errorMessage;
  final AudioPlayer _audioPlayer = AudioPlayer();
  List<CommentEntity> _comments = [];
  bool _isLoadingComments = false;

  /// Helper method to safely parse count values that might be strings or numbers
  int _parseCount(dynamic count) {
    if (count == null) return 0;
    if (count is int) return count;
    if (count is double) return count.toInt();
    if (count is String) {
      return int.tryParse(count) ?? 0;
    }
    return 0;
  }

  @override
  void initState() {
    super.initState();
    isLiked = widget.favorite;
    _initializeVideo();
  }

  @override
  void dispose() {
    // Performance optimization: Properly dispose of video controller to free memory
    _controller?.pause();
    _controller?.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }

  /// Pause video when widget is not visible (performance optimization)
  void pauseVideo() {
    if (_controller != null && _controller!.value.isInitialized) {
      _controller!.pause();
    }
  }

  /// Resume video when widget becomes visible
  void resumeVideo() {
    if (_controller != null && _controller!.value.isInitialized) {
      _controller!.play();
    }
  }

  Future<void> _initializeVideo() async {
    if (!mounted) return; // Performance: Check if widget is still mounted

    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
        _errorMessage = null;
      });

      // Performance optimization: Skip caching for better performance on low-memory devices
      // Use network URL directly to reduce memory usage and improve loading speed
      _controller = VideoPlayerController.networkUrl(
        Uri.parse(widget.videoUrl),
        httpHeaders: {
          'User-Agent': 'GatherPoint/1.0 (Flutter)',
          'Accept': 'video/mp4,video/*,*/*',
        },
        videoPlayerOptions: VideoPlayerOptions(
          // Performance optimizations for low-memory devices
          mixWithOthers: false,
          allowBackgroundPlayback: false,
        ),
      );

      // Add error listener before initialization
      _controller!.addListener(() {
        if (mounted && _controller!.value.hasError) {
          final error = _controller!.value.errorDescription;
          setState(() {
            _isLoading = false;
            _hasError = true;
            _errorMessage = error?.contains('codec') == true
                ? 'Video codec not supported on this device'
                : 'Failed to load video: ${error ?? 'Unknown error'}';
          });
        }
      });

      // Initialize the controller with timeout for better UX
      await _controller!.initialize().timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception('Video loading timeout');
        },
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = false;
        });

        // Performance optimization: Start with volume 0 to save resources
        _controller!.setVolume(0.0);
        _controller!.play();
        _controller!.setLooping(true);

        // Set actual volume after a brief delay
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted && _controller != null) {
            _controller!.setVolume(_isMuted ? 0.0 : 1.0);
          }
        });
      }

    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = e.toString().contains('timeout')
              ? 'Video loading timeout. Please check your connection.'
              : e.toString().contains('codec') || e.toString().contains('format')
                  ? 'Video codec not supported on this device'
                  : 'Failed to load video: ${e.toString()}';
        });
      }
    }
  }

  void _toggleMute() {
    setState(() {
      _isMuted = !_isMuted;
      _controller?.setVolume(_isMuted ? 0.0 : 1.0);
    });
  }

  Future<void> _loadComments() async {
    setState(() => _isLoadingComments = true);

    try {
      final response = await widget.dioConsumer.get(
        '/api/comments/${widget.id}',
      );

      if (response['data'] != null) {
        final List<dynamic> commentsData = response['data'];
        setState(() {
          _comments = commentsData
              .map((comment) => CommentEntity(
                    id: comment['id'],
                    videoId: widget.id,
                    userId: comment['user_id'],
                    userName: comment['user_name'] ?? 'Unknown User',
                    userAvatar: comment['user_avatar'] ?? '',
                    content: comment['content'],
                    createdAt: DateTime.parse(comment['created_at']),
                    updatedAt: DateTime.parse(comment['updated_at']),
                    likesCount: comment['likes_count'] ?? 0,
                    isLiked: comment['is_liked'] ?? false,
                    replies: [], // TODO: Implement replies if needed
                  ))
              .toList();
        });
      }
    } catch (e) {
      debugPrint('Error loading comments: $e');
    } finally {
      setState(() => _isLoadingComments = false);
    }
  }

  Future<void> _postComment(String content) async {
    try {
      final response = await widget.dioConsumer.post(
        '/api/comments',
        data: {
          'service_category_item_id': widget.id,
          'content': content,
        },
      );

      if (response['data'] != null) {
        await _loadComments(); // Reload comments
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(S.of(context).commentPosted),
              backgroundColor: AppColors.successGreen,
            ),
          );
        }
      }
    } catch (e) {
      throw Exception('Failed to post comment');
    }
  }

  Future<void> _likeComment(int commentId) async {
    try {
      await widget.dioConsumer.post(
        '/api/comments/$commentId/like',
      );
      await _loadComments(); // Reload comments
    } catch (e) {
      debugPrint('Error liking comment: $e');
    }
  }

  Future<void> _deleteComment(int commentId) async {
    try {
      await widget.dioConsumer.delete(
        '/api/comments/$commentId',
      );
      await _loadComments(); // Reload comments
    } catch (e) {
      debugPrint('Error deleting comment: $e');
    }
  }

  void _showCommentDialog() {
    _loadComments(); // Load comments when dialog opens

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CommentDialog(
        videoId: widget.id,
        comments: _comments,
        onCommentPost: _postComment,
        onCommentLike: _likeComment,
        onCommentDelete: _deleteComment,
      ),
    );
  }

  // Check if user is authenticated (not a guest)
  bool _isUserAuthenticated() {
    try {
      final userBox = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
      final user = userBox.get(AppConstants.kMyProfileKey);

      // Check if user exists and is not a guest
      return user != null && !user.isGuest;
    } catch (e) {
      return false;
    }
  }

  // Show login required dialog
  void _showLoginRequiredDialog(BuildContext context) {
    final s = S.of(context);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDark ? AppColors.darkGrey : AppColors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'تسجيل الدخول مطلوب',
          style: AppTextStyles.font18Bold.copyWith(
            color: ThemeHelper.getPrimaryTextColor(context),
          ),
        ),
        content: Text(
          'يجب تسجيل الدخول أولاً لإجراء حجز',
          style: AppTextStyles.font14Regular.copyWith(
            color: ThemeHelper.getSecondaryTextColor(context),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              s.cancel,
              style: AppTextStyles.font14Medium.copyWith(
                color: ThemeHelper.getSecondaryTextColor(context),
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to login screen
              context.push(RoutesKeys.kLoginView);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.yellow,
              foregroundColor: AppColors.black,
            ),
            child: Text(
              'تسجيل الدخول',
              style: AppTextStyles.font14SemiBold.copyWith(
                color: AppColors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showReservationDialog(BuildContext context) {
    // Check if user is authenticated first
    if (!_isUserAuthenticated()) {
      _showLoginRequiredDialog(context);
      return;
    }

    DateTime? startDate;
    DateTime? endDate;
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              backgroundColor: isDark ? AppColors.darkGrey : AppColors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              title: Text(
                S.of(context).selectReservationDate,
                style: AppTextStyles.font18Bold.copyWith(
                  color: ThemeHelper.getPrimaryTextColor(context),
                ),
              ),
              content: SizedBox(
                width: 500,
                height: 400,
                child: CalendarWidget(onDateSelected:
                    (DateTime? start, DateTime? end, double totalPrice) {
                  startDate = start;
                  endDate = end;
                }),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  style: TextButton.styleFrom(
                    foregroundColor: ThemeHelper.getSecondaryTextColor(context),
                  ),
                  child: Text(
                    S.of(context).cancel,
                    style: AppTextStyles.font14Medium.copyWith(
                      color: ThemeHelper.getSecondaryTextColor(context),
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: () async {
                    if (startDate != null && endDate != null) {
                      await _getReservationInfo(context, startDate!, endDate!);
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            S.of(context).pleaseSelectBothDates,
                            style: AppTextStyles.font14Regular.copyWith(
                              color: AppColors.white,
                            ),
                          ),
                          backgroundColor:
                              isDark ? AppColors.darkGrey2 : AppColors.darkGrey,
                        ),
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.yellow,
                    foregroundColor: AppColors.black,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    S.of(context).reviewReservation,
                    style: AppTextStyles.font14SemiBold.copyWith(
                      color: AppColors.black,
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _getReservationInfo(
      BuildContext context, DateTime startDate, DateTime endDate) async {
    try {
      final response = await widget.dioConsumer.post(
        '/api/reservations/check',
        data: {
          'service_category_item_id': widget.id,
          'reservation_from':
              DateFormat('yyyy-MM-dd HH:mm:ss', 'en-US').format(startDate),
          'reservation_to':
              DateFormat('yyyy-MM-dd HH:mm:ss', 'en-US').format(endDate),
        },
      );

      if (!mounted) return;

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ReservationConfirmationPage(
            reservationData: response['data'],
            dioConsumer: widget.dioConsumer,
            videoId: widget.id,
            reservationFrom:
                DateFormat('yyyy-MM-dd HH:mm:ss', 'en-US').format(startDate),
            reservationTo:
                DateFormat('yyyy-MM-dd HH:mm:ss', 'en-US').format(endDate),
          ),
        ),
      );
    } on DioException catch (e) {
      if (!mounted) return;

      showDialog(
        context: context,
        builder: (BuildContext dialogContext) {
          final theme = Theme.of(dialogContext);
          final isDark = theme.brightness == Brightness.dark;

          return AlertDialog(
            backgroundColor: isDark ? AppColors.darkGrey : AppColors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Text(
              S.of(dialogContext).error,
              style: AppTextStyles.font18Bold.copyWith(
                color: ThemeHelper.getPrimaryTextColor(dialogContext),
              ),
            ),
            content: Text(
              e.response?.statusCode == 409
                  ? S.of(dialogContext).selectedPeriodNotAvailable
                  : "${S.of(dialogContext).errorFetchingInfo}: ${e.message}",
              style: AppTextStyles.font14Regular.copyWith(
                color: ThemeHelper.getSecondaryTextColor(dialogContext),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(),
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.yellow,
                ),
                child: Text(
                  S.of(dialogContext).ok,
                  style: AppTextStyles.font14SemiBold.copyWith(
                    color: AppColors.yellow,
                  ),
                ),
              ),
            ],
          );
        },
      );
    }
  }

  void _navigateToDetails(BuildContext context) {
    // Navigate to place details screen using the item ID
    // The PlaceDetailsScreen will load full details from the API
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlaceDetailsScreen(
          placeId: widget.id, // Use placeId to load full details from API
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final isRTL = context.read<LocaleCubit>().isArabic();

    return Scaffold(
      backgroundColor: isDark ? AppColors.black : AppColors.white,
      body: RepaintBoundary(
        child: Stack(
          children: [
            // Video player section - optimized with RepaintBoundary
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.yellow),
                  strokeWidth: 2, // Reduced stroke width for performance
                ),
              )
            else if (_hasError)
              Center(
                child: Text(
                  S.of(context).failedToLoadVideo,
                  style: AppTextStyles.font16Regular.copyWith(
                    color: AppColors.white,
                  ),
                ),
              )
            else
              RepaintBoundary(
                child: GestureDetector(
                  onTap: () {
                    if (_controller != null && _controller!.value.isInitialized) {
                      setState(() {
                        _controller!.value.isPlaying
                            ? _controller!.pause()
                            : _controller!.play();
                      });
                    }
                  },
                  child: SizedBox.expand(
                    child: _controller != null && _controller!.value.isInitialized
                        ? FittedBox(
                            fit: BoxFit.cover,
                            child: SizedBox(
                              width: _controller!.value.size.width,
                              height: _controller!.value.size.height,
                              child: VideoPlayer(_controller!),
                            ),
                          )
                        : Container(
                            color: Colors.black,
                            child: const Center(
                              child: Icon(
                                Icons.play_circle_outline,
                                color: Colors.white,
                                size: 64,
                              ),
                            ),
                          ),
                  ),
                ),
              ),
          // Right side controls - optimized with RepaintBoundary
          RepaintBoundary(
            child: Positioned(
              left: isRTL ? 20 : null,
              right: isRTL ? null : 20,
              bottom: 90,
              child: Column(
              children: [
                IconButton(
                  icon: Icon(
                    _isMuted ? Icons.volume_off : Icons.volume_up,
                    color: AppColors.white,
                    size: 40,
                  ),
                  onPressed: _toggleMute,
                  tooltip: _isMuted
                      ? S.of(context).unmuteVideo
                      : S.of(context).muteVideo,
                ),
                const SizedBox(height: 20),
                IconButton(
                  icon: Icon(
                    isLiked ? Icons.favorite_sharp : Icons.favorite_border_outlined,
                    size: 40,
                    color: isLiked ? AppColors.red : AppColors.white,
                  ),
                  onPressed: () async {
                    final current = isLiked;
                    setState(() => isLiked = !isLiked);
                    try {
                      await widget.dioConsumer.post(
                        '/api/favorite/set',
                        data: {'service_category_item_id': widget.id},
                      );
                    } catch (_) {
                      setState(() => isLiked = current);
                    }
                  },
                  tooltip: isLiked
                      ? S.of(context).removeFromFavorites
                      : S.of(context).addToFavorites,
                ),
                const SizedBox(height: 20),
                IconButton(
                  icon: const Icon(
                    Icons.comment_bank,
                    color: AppColors.white,
                    size: 40,
                  ),
                  onPressed: _showCommentDialog,
                  tooltip: S.of(context).comment,
                ),
                const SizedBox(height: 20),
                IconButton(
                  icon:  Image.asset(
                    AppAssets.iconsShare,
                    color: AppColors.white,
                    width: 40,
                    height: 40,
                  ),
                  onPressed: () {
                    // TODO: Implement share functionality
                  },
                  tooltip: S.of(context).share,
                ),
                const SizedBox(height: 20),
                IconButton(
                  icon: const Icon(
                    Icons.info_rounded,
                    color: AppColors.white,
                    size: 40,
                  ),
                  onPressed: () => _navigateToDetails(context),
                  tooltip: S.of(context).details,
                ),
              ],
            ),
            ),
          ),
          // Facilities section - optimized with RepaintBoundary
          RepaintBoundary(
            child: widget.facilities
                    .where((facility) => _parseCount(facility['count']) > 0)
                    .toList()
                    .isEmpty
                ? const SizedBox.shrink()
                : Positioned(
                  bottom: 80,
                  left: isRTL ? null : 20,
                  right: isRTL ? 20 : null,
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.black.withValues(alpha: 0.4),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.white.withValues(alpha: 0.1),
                        width: 1,
                      ),
                    ),
                    child: Builder(builder: (context) {
                      final filteredFacilities = widget.facilities
                          .where(
                              (facility) => _parseCount(facility['count']) > 0)
                          .toList();

                      return Column(
                        children: [
                          if (filteredFacilities.isNotEmpty)
                            Row(
                              children:
                                  filteredFacilities.take(2).map((facility) {
                                return Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10),
                                  child: FacilityIcon(
                                    iconUrl: facility['icon'],
                                    title: '${_parseCount(facility['count'])}',
                                    iconSize: 60,
                                  ),
                                );
                              }).toList(),
                            ),
                          if (filteredFacilities.length > 2)
                            const SizedBox(height: 20),
                          if (filteredFacilities.length > 2)
                            Row(
                              children: filteredFacilities
                                  .skip(2)
                                  .take(2)
                                  .map((facility) {
                                return Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10),
                                  child: FacilityIcon(
                                    iconUrl: facility['icon'],
                                    title:
                                        '${facility['count']} ${facility['title']}',
                                    iconSize: 30,
                                  ),
                                );
                              }).toList(),
                            ),
                        ],
                      );
                    }),
                  ),
                ),
          ),

          // Reservation button - optimized with RepaintBoundary
          RepaintBoundary(
            child: Positioned(
              bottom: 15,
              left: 20,
              right: 20,
              child: ElevatedButton(
              onPressed: () => _showReservationDialog(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.yellow,
                foregroundColor: AppColors.black,
                padding: const EdgeInsets.symmetric(vertical: 16),
                elevation: 8,
                shadowColor: AppColors.yellow.withValues(alpha: 0.3),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: Text(
                S.of(context).bookNow,
                style: AppTextStyles.font16Bold.copyWith(
                  color: AppColors.black,
                  letterSpacing: 0.5,
                ),
              ),
            ),
            ),
          ),
          // Video progress indicator - optimized with RepaintBoundary
          if (!_isLoading && !_hasError && _controller != null && _controller!.value.isInitialized)
            RepaintBoundary(
              child: Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  child: VideoProgressIndicator(
                    _controller!,
                    allowScrubbing: true,
                    colors: VideoProgressColors(
                      playedColor: AppColors.yellow,
                      bufferedColor: AppColors.white.withValues(alpha: 0.4),
                      backgroundColor: AppColors.black.withValues(alpha: 0.3),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
      ),
    );
  }
}

class ServiceCategory {
  final String title;
  final String icon;

  ServiceCategory({required this.title, required this.icon});

  factory ServiceCategory.fromJson(Map<String, dynamic> json) {
    return ServiceCategory(
      title: json['title'] ?? '',
      icon: json['icon'] ?? '',
    );
  }
}
