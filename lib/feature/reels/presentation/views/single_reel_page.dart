import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/managers/locale_cubit/locale_cubit.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/reels/presentation/views/widgets/video_player_widget.dart';
import 'package:hive/hive.dart';
import 'package:dio/dio.dart';

class SingleReelPage extends StatefulWidget {
  final List<Map<String, dynamic>> reelsData; // All reels data
  final int selectedIndex; // Index of the selected reel
  final bool showBackButton; // Whether to show back button

  const SingleReelPage({
    super.key,
    required this.reelsData,
    required this.selectedIndex,
    this.showBackButton = true,
  });

  @override
  State<SingleReelPage> createState() => _SingleReelPageState();
}

class _SingleReelPageState extends State<SingleReelPage> {
  late final DioConsumer dioConsumer;
  late final PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    dioConsumer = DioConsumer(
      dio: getIt<Dio>(),
      profileBox: getIt<Box<UserEntity>>(),
    );
    _currentIndex = widget.selectedIndex;
    _pageController = PageController(initialPage: widget.selectedIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// Helper method to safely parse price values that might be strings or numbers
  double _parsePrice(dynamic price) {
    if (price == null) return 0.0;
    if (price is double) return price;
    if (price is int) return price.toDouble();
    if (price is String) {
      return double.tryParse(price) ?? 0.0;
    }
    return 0.0;
  }

  Widget _buildReels(bool isRTL) {
    return SafeArea(
      child: Builder(
        builder: (context) {
          return Stack(
            children: [
              // Main Video Content
              PageView.builder(
                controller: _pageController,
                scrollDirection: Axis.vertical,
                itemCount: widget.reelsData.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentIndex = index;
                  });
                },
                itemBuilder: (context, index) {
                  final item = widget.reelsData[index];
                  return VideoPlayerWidget(
                    facilities: item['facilities'] ?? [],
                    videoUrl: item['video'],
                    title: item['title'],
                    location: item['title'],
                    id: item['id'],
                    price: _parsePrice(item['price']),
                    serviceCategoryId: 2,
                    favorite: item['favorite'],
                    dioConsumer: dioConsumer,
                  );
                },
              ),

              // Back button overlay
              if (widget.showBackButton)
                Positioned(
                  top: 16,
                  left: isRTL ? null : 16,
                  right: isRTL ? 16 : null,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.4),
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: IconButton(
                      onPressed: () => Navigator.of(context).maybePop(),
                      icon: const Icon(
                        Icons.arrow_back_ios_rounded,
                        color: Colors.white,
                        size: 20,
                      ),
                      padding: const EdgeInsets.all(8),
                    ),
                  ),
                ),

              // Reel indicator (optional)
              if (widget.reelsData.length > 1)
                Positioned(
                  bottom: 100,
                  right: 16,
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.5),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${_currentIndex + 1}/${widget.reelsData.length}',
                          style: AppTextStyles.font12Regular.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final isRTL = context.read<LocaleCubit>().isArabic();
    debugPrint('isRTL: $isRTL');
    // Set status bar based on theme
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        systemNavigationBarColor:
            isDark ? Colors.black : theme.scaffoldBackgroundColor,
        systemNavigationBarIconBrightness:
            isDark ? Brightness.light : Brightness.dark,
      ),
    );

    return Scaffold(
      backgroundColor: isDark ? Colors.black : theme.scaffoldBackgroundColor,
      body: _buildReels(isRTL),
    );
  }
}
