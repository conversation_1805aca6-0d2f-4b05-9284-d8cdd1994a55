import 'package:flutter/material.dart';
import 'package:gather_point/core/utils/auth_utils.dart';
import 'package:gather_point/feature/auth/presentation/views/login_view.dart';
import 'package:gather_point/feature/home/<USER>/models/place_detail_model.dart';
import 'package:gather_point/feature/home/<USER>/views/reserve_screen.dart';
import 'package:gather_point/generated/l10n.dart';

/// Handler for guest mode reservations and authentication flows
class GuestReservationHandler {
  
  /// Handle reservation flow for both guest and authenticated users
  static Future<void> handleReservation({
    required BuildContext context,
    required PlaceDetailModel place,
    VoidCallback? onLoginSuccess,
  }) async {

    // Check if user can make reservations
    if (!AuthUtils.canMakeReservation()) {
      await _showLoginRequiredDialog(
        context: context,
        title: 'تسجيل الدخول مطلوب',
        message: 'تحتاج إلى تسجيل الدخول لإجراء حجز. يمكن للضيوف أيضاً إجراء حجوزات بميزات محدودة.',
        onLoginSuccess: onLoginSuccess,
      );
      return;
    }

    // Check if user is guest but has valid token
    if (AuthUtils.isGuest() && AuthUtils.hasValidToken()) {
      await _showGuestReservationDialog(
        context: context,
        place: place,
        onProceedAsGuest: () => _navigateToReservation(context, place),
        onLoginSuccess: onLoginSuccess,
      );
      return;
    }

    // User is authenticated, proceed directly
    _navigateToReservation(context, place);
  }

  /// Handle favorite action for both guest and authenticated users
  static Future<void> handleFavorite({
    required BuildContext context,
    required PlaceDetailModel place,
    required VoidCallback onToggleFavorite,
    VoidCallback? onLoginSuccess,
  }) async {

    if (!AuthUtils.canAddToFavorites()) {
      await _showLoginRequiredDialog(
        context: context,
        title: 'تسجيل الدخول مطلوب',
        message: 'تحتاج إلى تسجيل الدخول لإضافة العناصر إلى قائمة المفضلة.',
        onLoginSuccess: () {
          onToggleFavorite();
          onLoginSuccess?.call();
        },
      );
      return;
    }

    // User is authenticated, proceed with favorite toggle
    onToggleFavorite();
  }

  /// Handle review action for both guest and authenticated users
  static Future<void> handleReview({
    required BuildContext context,
    required PlaceDetailModel place,
    required VoidCallback onWriteReview,
    VoidCallback? onLoginSuccess,
  }) async {

    if (!AuthUtils.canWriteReviews()) {
      await _showLoginRequiredDialog(
        context: context,
        title: 'تسجيل الدخول مطلوب',
        message: 'تحتاج إلى تسجيل الدخول لكتابة التقييمات ومشاركة تجربتك.',
        onLoginSuccess: () {
          onWriteReview();
          onLoginSuccess?.call();
        },
      );
      return;
    }

    // User is authenticated, proceed with review
    onWriteReview();
  }

  /// Show login required dialog
  static Future<void> _showLoginRequiredDialog({
    required BuildContext context,
    required String title,
    required String message,
    VoidCallback? onLoginSuccess,
  }) async {
    final s = S.of(context);

    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.login, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              Text(title),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(message),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue[700], size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'كضيف، يمكنك التصفح والحجز، لكن بعض الميزات تتطلب حساب',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.blue[700],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(s.cancel),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _navigateToLogin(context, onLoginSuccess);
              },
              child: Text(s.login),
            ),
          ],
        );
      },
    );
  }

  /// Show guest reservation dialog
  static Future<void> _showGuestReservationDialog({
    required BuildContext context,
    required PlaceDetailModel place,
    required VoidCallback onProceedAsGuest,
    VoidCallback? onLoginSuccess,
  }) async {
    final s = S.of(context);

    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.person_outline, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              const Text('حجز كضيف'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('أنت تتصفح حالياً كضيف. يمكنك المتابعة مع الحجز، لكن إنشاء حساب سيمنحك المزيد من الميزات.'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.warning_amber, color: Colors.orange[700], size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'قيود الضيف:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.orange[700],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• لا يمكن حفظ المفضلة\n• لا يمكن كتابة التقييمات\n• تاريخ حجوزات محدود\n• لا يمكن إدارة الملف الشخصي',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange[700],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(s.cancel),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _navigateToLogin(context, onLoginSuccess);
              },
              child: const Text('تسجيل دخول لتجربة أفضل'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onProceedAsGuest();
              },
              child: const Text('متابعة كضيف'),
            ),
          ],
        );
      },
    );
  }

  /// Navigate to reservation screen
  static void _navigateToReservation(BuildContext context, PlaceDetailModel place) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ReserveScreen(
          serviceCategoryItemId: place.id,
          placeTitle: place.title,
          bookingRules: place.bookingRules,
          cancelationRules: place.cancelationRules,
          cancellationPolicy: place.cancellationPolicy,
          pricePerNight: place.price,
        ),
      ),
    );
  }

  /// Navigate to login screen
  static void _navigateToLogin(BuildContext context, VoidCallback? onSuccess) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const LoginView(),
      ),
    ).then((_) {
      // Check if user logged in successfully
      if (AuthUtils.isAuthenticated()) {
        onSuccess?.call();
      }
    });
  }

  /// Show feature unavailable for guests dialog
  static Future<void> showFeatureUnavailableDialog({
    required BuildContext context,
    required String feature,
    VoidCallback? onLoginSuccess,
  }) async {
    final s = S.of(context);

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.lock_outline, color: Colors.orange[700]),
              const SizedBox(width: 8),
              const Text('الميزة غير متاحة'),
            ],
          ),
          content: Text('هذه الميزة تتطلب تسجيل الدخول. يرجى إنشاء حساب أو تسجيل الدخول للوصول إلى $feature.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(s.cancel),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _navigateToLogin(context, onLoginSuccess);
              },
              child: Text(s.login),
            ),
          ],
        );
      },
    );
  }

  /// Get user status badge for UI
  static Widget getUserStatusBadge(BuildContext context) {
    if (AuthUtils.isAuthenticated()) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.green.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.verified_user, size: 14, color: Colors.green[700]),
            const SizedBox(width: 4),
            Text(
              AuthUtils.getDisplayName(),
              style: TextStyle(
                fontSize: 12,
                color: Colors.green[700],
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.orange.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.person_outline, size: 14, color: Colors.orange[700]),
            const SizedBox(width: 4),
            const Text(
              'ضيف',
              style: TextStyle(
                fontSize: 12,
                color: Colors.orange,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      );
    }
  }
}
